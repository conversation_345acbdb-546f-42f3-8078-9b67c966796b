import React from 'react';
import { getImageUrlFromName, getImageUrlsFromNames } from '../../frontend/Customer/aws/s3FileUpload';

interface ReviewImagesProps {
  imageNames?: string[];
  imageUrls?: string[];
  maxDisplay?: number;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showCount?: boolean;
  folderName?: string;
  layout?: 'row' | 'column' | 'grid-4';
}

const ReviewImages: React.FC<ReviewImagesProps> = ({
  imageNames = [],
  imageUrls = [],
  maxDisplay = 3,
  size = 'md',
  className = '',
  showCount = true,
  folderName = 'review-images',
  layout = 'row'
}) => {
  // Determine which images to display - prioritize imageNames over imageUrls
  const displayImages = React.useMemo(() => {
    if (imageNames.length > 0) {
      // Convert image names to URLs
      return getImageUrlsFromNames(imageNames, folderName);
    } else if (imageUrls.length > 0) {
      // Use provided URLs as fallback
      return imageUrls;
    }
    return [];
  }, [imageNames, imageUrls, folderName]);

  // Get size classes
  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-32 h-32',
    xl: 'w-40 h-40'
  };

  const imageSize = sizeClasses[size];
  const totalImages = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  // Don't render if no images
  if (displayImages.length === 0) {
    return null;
  }

  // Determine layout classes
  const layoutClasses = layout === 'column'
    ? 'flex flex-col gap-4'
    : layout === 'grid-4'
    ? 'grid grid-cols-4 gap-4'
    : 'flex flex-wrap gap-3';

  return (
    <div className={`${layoutClasses} ${className}`}>
      {/* Display images up to maxDisplay limit */}
      {displayImages.slice(0, maxDisplay).map((imageUrl, idx) => (
        <div key={idx} className="relative">
          <img
            src={imageUrl}
            alt={`Review image ${idx + 1}`}
            className={`${imageSize} object-cover rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow`}
            onError={(e) => {
              // Fallback for broken images
              const target = e.target as HTMLImageElement;
              target.src = 'https://via.placeholder.com/100x100?text=Image+Not+Found';
            }}
          />
          {/* Optional overlay for image source indicator */}
          <div className="absolute bottom-0 left-0 bg-black bg-opacity-50 text-white text-xs px-1 rounded-br-lg rounded-tl-lg">
            {imageNames.length > 0 ? 'S3' : 'URL'}
          </div>
        </div>
      ))}

      {/* Show count indicator if there are more images */}
      {showCount && totalImages > maxDisplay && (
        <div className={`${imageSize} bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center text-xs text-gray-500 font-medium hover:bg-gray-200 transition-colors`}>
          +{totalImages - maxDisplay}
        </div>
      )}
    </div>
  );
};

export default ReviewImages;

// Hook for managing review images
export const useReviewImages = (imageNames: string[], imageUrls: string[], folderName = 'review-images') => {
  const displayUrls = React.useMemo(() => {
    if (imageNames.length > 0) {
      return getImageUrlsFromNames(imageNames, folderName);
    }
    return imageUrls;
  }, [imageNames, imageUrls, folderName]);

  const hasImages = imageNames.length > 0 || imageUrls.length > 0;
  const totalCount = imageNames.length > 0 ? imageNames.length : imageUrls.length;

  return {
    displayUrls,
    hasImages,
    totalCount,
    imageNames,
    imageUrls
  };
};
