import { useState, useEffect, useMemo, useCallback } from 'react';
import { FiEdit, FiTrash, FiCheckCircle, FiXCircle, FiAlertCircle, FiStar, FiEye, FiChevronLeft, FiChevronRight, FiChevronsLeft, FiChevronsRight } from 'react-icons/fi';
import { Pagination, Button, Spinner, Card, CardBody, Chip, Select, SelectItem, Tabs, Tab, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea } from '@heroui/react';
import { useAuth } from 'react-oidc-context';
import BreadCrumb from '../../../components/common/breadcrumb/breadCrumb';
import ReviewForm, { ReviewData } from '../../../../feature-module/components/ReviewForm/ReviewForm';
import {
  Review,
  getAllReviews,
  createReview,
  updateReview,
  deleteReview,
  CreateReviewData,
  UpdateReviewData
} from '../../../../service/reviewService';
import ReviewImages from '../../../components/ReviewImages/ReviewImages';
import './Reviews.css';

type NotificationType = 'success' | 'error' | 'warning' | 'info';

interface Notification {
  type: NotificationType;
  message: string;
  id: string;
  duration?: number;
}

const Reviews = () => {
  const auth = useAuth();







  const defaultReviews = useMemo(() => [
    {
      id: 'review-1',
      title: 'Building Construction Services',
      name: 'Jeffrey Adang',
      email: '<EMAIL>',
      userName: 'Jeffrey Adang',
      userEmail: '<EMAIL>',
      isVerified: true,
      date: new Date('2023-08-25T14:30:00').toISOString(),
      createdAt: new Date('2023-08-25T14:30:00').toISOString(),
      rating: 5,
      review:
        'The construction service delivered excellent craftsmanship, completing my home renovation on time with clear communication throughout. The team was professional, punctual, and maintained a clean work environment. I highly recommend their services for any construction needs.',
      images: [],
      imageUrls: [
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
      ],
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
      userProfileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
      serviceId: '101',
      serviceRating: 5,
      qualityRating: 5,
      valueRating: 4,
      communicationRating: 5,
    },
    {
      id: 'review-2',
      title: 'Plumbing Services',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      userName: 'Sarah Johnson',
      userEmail: '<EMAIL>',
      isVerified: true,
      date: new Date('2023-07-15T09:45:00').toISOString(),
      createdAt: new Date('2023-07-15T09:45:00').toISOString(),
      rating: 4,
      review:
        'The plumbing service was efficient and reliable. The technician arrived on time, quickly diagnosed the issue, and provided a fair quote. The work was completed professionally with excellent attention to detail. Very satisfied with the quality of service.',
      images: [],
      imageUrls: [
        'https://via.placeholder.com/100',
        'https://via.placeholder.com/100',
      ],
      profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
      userProfileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
      serviceId: '102',
      serviceRating: 4,
      qualityRating: 4,
      valueRating: 4,
      communicationRating: 4,
    },
  ], []);

  const [reviews, setReviews] = useState<Review[]>([]);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalReviews, setTotalReviews] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [filterRating, setFilterRating] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<string>('show-reviews');
  const [reviewsPerPage, setReviewsPerPage] = useState(3); // Updated to 3 reviews per page as requested

  // View modal state
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedViewReview, setSelectedViewReview] = useState<Review | null>(null);

  // Comment editing state
  const [editingComment, setEditingComment] = useState(false);
  const [editedComment, setEditedComment] = useState('');

  // Function to add a notification
  const addNotification = (type: NotificationType, message: string, duration = 3000) => {
    const id = `notification-${Date.now()}`;
    const newNotification: Notification = { type, message, id, duration };
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(notification => notification.id !== id));
    }, duration);
  };

  // Function to handle viewing a review
  const handleViewReview = (review: Review) => {
    setSelectedViewReview(review);
    setShowViewModal(true);
  };

  // Function to close view modal
  const handleCloseViewModal = () => {
    setShowViewModal(false);
    setSelectedViewReview(null);
    setEditingComment(false);
    setEditedComment('');
  };

  // Comment editing functions
  const handleStartEditComment = () => {
    if (selectedViewReview) {
      setEditingComment(true);
      setEditedComment(selectedViewReview.review || selectedViewReview.comment || '');
    }
  };

  const handleCancelEditComment = () => {
    setEditingComment(false);
    setEditedComment('');
  };

  const handleSaveComment = async () => {
    if (!selectedViewReview || !editedComment.trim()) {
      addNotification('error', 'Comment cannot be empty');
      return;
    }

    if (editedComment.trim().length < 10) {
      addNotification('error', 'Comment must be at least 10 characters long');
      return;
    }

    if (editedComment.trim().length > 2000) {
      addNotification('error', 'Comment must be less than 2000 characters');
      return;
    }

    try {
      setLoading(true);
      const reviewId = selectedViewReview.id || selectedViewReview._id;

      if (!reviewId) {
        addNotification('error', 'Cannot update review: Invalid review ID');
        return;
      }

      const updateData: UpdateReviewData = {
        comment: editedComment.trim(),
        // Keep all other existing data
        title: selectedViewReview.title,
        rating: selectedViewReview.rating,
        providerId: selectedViewReview.providerId,
        serviceId: selectedViewReview.serviceId,
        bookingId: selectedViewReview.bookingId,
        imageNames: selectedViewReview.imageNames || [],
        serviceRating: selectedViewReview.serviceRating,
        qualityRating: selectedViewReview.qualityRating,
        valueRating: selectedViewReview.valueRating,
        communicationRating: selectedViewReview.communicationRating,
        timelinessRating: selectedViewReview.timelinessRating
      };

      const updatedReview = await updateReview(reviewId, updateData);

      // Update the review in the list
      setReviews(prevReviews =>
        prevReviews.map(review =>
          (review.id || review._id) === reviewId
            ? { ...review, review: editedComment.trim(), comment: editedComment.trim(), updatedAt: new Date().toISOString() }
            : review
        )
      );

      // Update the selected view review
      setSelectedViewReview(prev => prev ? {
        ...prev,
        review: editedComment.trim(),
        comment: editedComment.trim(),
        updatedAt: new Date().toISOString()
      } : null);

      setEditingComment(false);
      setEditedComment('');
      addNotification('success', 'Comment updated successfully!');
    } catch (error) {
      console.error('Error updating comment:', error);
      addNotification('error', 'Failed to update comment. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to handle reviews per page change
  const handleReviewsPerPageChange = (newPerPage: number) => {
    setReviewsPerPage(newPerPage);
    setCurrentPage(1); // Reset to first page when changing per page
    fetchReviews(1); // Fetch first page with new limit
  };

  // Keyboard navigation for pagination
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target instanceof HTMLSelectElement) {
        return; // Don't handle keyboard events when user is typing in inputs
      }

      switch (event.key) {
        case 'ArrowLeft':
          if (currentPage > 1) {
            event.preventDefault();
            goToPreviousPage();
          }
          break;
        case 'ArrowRight':
          if (currentPage < totalPages) {
            event.preventDefault();
            goToNextPage();
          }
          break;
        case 'Home':
          if (totalPages > 1) {
            event.preventDefault();
            goToFirstPage();
          }
          break;
        case 'End':
          if (totalPages > 1) {
            event.preventDefault();
            goToLastPage();
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentPage, totalPages]);

  // Calculate review statistics
  const reviewStats = useMemo(() => {
    const allReviews = [...reviews, ...defaultReviews];
    const totalCount = allReviews.length;
    const averageRating = totalCount > 0
      ? allReviews.reduce((sum, review) => sum + review.rating, 0) / totalCount
      : 0;

    const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
      rating,
      count: allReviews.filter(review => review.rating === rating).length,
      percentage: totalCount > 0 ? (allReviews.filter(review => review.rating === rating).length / totalCount) * 100 : 0
    }));

    return {
      totalCount,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution
    };
  }, [reviews, defaultReviews]);

  // Filter reviews based on rating
  const filteredReviews = useMemo(() => {
    if (filterRating === 'all') return reviews;
    return reviews.filter(review => review.rating === parseInt(filterRating));
  }, [reviews, filterRating]);

  // Fetch reviews from API
  const fetchReviews = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      // Always fetch all reviews for "Show Reviews" tab
      const response = await getAllReviews({
        page,
        limit: reviewsPerPage // Use dynamic reviews per page
      });

      // Debug: Log the actual review structure to see what properties are available
      console.log('Fetched reviews response:', response);
      if (response.reviews && response.reviews.length > 0) {
        console.log('First review structure:', response.reviews[0]);
        console.log('Review ID property:', response.reviews[0].id || response.reviews[0]._id);
      }

      setReviews(response.reviews || []);
      setTotalReviews(response.total || 0);
      setTotalPages(response.totalPages || 0);
      setCurrentPage(response.page || 1);

    } catch (error: any) {
      console.error('Error fetching reviews:', error);
      setError('Failed to load reviews. Please try again.');
      addNotification('error', 'Failed to load reviews. Please try again.');

      // Fallback to empty array
      setReviews([]);
      setTotalReviews(0);
      setTotalPages(0);
    } finally {
      setLoading(false);
    }
  }, [reviewsPerPage]);

  // Load reviews when component mounts or tab changes
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchReviews(1); // Reset to page 1 when tab changes
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchReviews, activeTab]);

  // Load reviews when page changes (but not tab)
  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading && activeTab === 'show-reviews') {
      fetchReviews(currentPage);
    }
  }, [currentPage, auth.isAuthenticated, auth.isLoading, activeTab, fetchReviews]);

  // For server-side pagination, we use all reviews from the current page
  const currentReviews = reviews;

  // Change page - now fetches new data from API
  const handlePageChange = useCallback((page: number) => {
    if (page !== currentPage && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      fetchReviews(page);
      // Scroll to top when changing pages
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [currentPage, totalPages, fetchReviews]);

  // Pagination helper functions
  const goToFirstPage = useCallback(() => handlePageChange(1), [handlePageChange]);
  const goToLastPage = useCallback(() => handlePageChange(totalPages), [handlePageChange, totalPages]);
  const goToPreviousPage = useCallback(() => handlePageChange(currentPage - 1), [handlePageChange, currentPage]);
  const goToNextPage = useCallback(() => handlePageChange(currentPage + 1), [handlePageChange, currentPage]);

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total pages is less than or equal to max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages with ellipsis
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      let endPage = Math.min(totalPages, currentPage + halfVisible);

      // Adjust if we're near the beginning or end
      if (currentPage <= halfVisible) {
        endPage = Math.min(totalPages, maxVisiblePages);
      } else if (currentPage > totalPages - halfVisible) {
        startPage = Math.max(1, totalPages - maxVisiblePages + 1);
      }

      // Add first page and ellipsis if needed
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }

      // Add visible pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis and last page if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  };





  const handleSubmitReview = async (reviewData: ReviewData) => {
    try {
      setLoading(true);
      addNotification('info', 'Processing your review submission...');

      if (isEditMode && selectedReview) {
        // Update existing review
        const updateData: UpdateReviewData = {
          title: reviewData.title,
          review: reviewData.review,
          rating: reviewData.rating,
          imageUrls: reviewData.imageUrls,
          imageNames: reviewData.imageNames || [], // Include image names for backend storage
        };

        // Only include rating fields that have valid values (1-4)
        if (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4) {
          updateData.serviceRating = Math.round(reviewData.serviceRating);
        }
        if (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4) {
          updateData.qualityRating = Math.round(reviewData.qualityRating);
        }
        if (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4) {
          updateData.valueRating = Math.round(reviewData.valueRating);
        }
        if (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4) {
          updateData.communicationRating = Math.round(reviewData.communicationRating);
        }

        // Backend requires timelinessRating - use overall rating as default if not provided
        if (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4) {
          updateData.timelinessRating = Math.round(reviewData.timelinessRating);
        } else {
          updateData.timelinessRating = Math.round(reviewData.rating);
        }

        // Get the review ID (handle both id and _id)
        const reviewId = selectedReview.id || selectedReview._id;
        if (!reviewId) {
          throw new Error('Cannot update review: Invalid review ID');
        }

        console.log('Updating review with data:', updateData);
        await updateReview(reviewId, updateData);

        // Refresh the current page to show updated review
        await fetchReviews(currentPage);

        // Show success notification
        addNotification('success', `Your review for "${reviewData.title}" has been updated successfully with ${updateData.imageNames?.length || 0} images.`);
      } else {
        // Add new review with user information
        const currentDate = new Date().toISOString();

        // Validate required fields before creating review
        if (!reviewData.serviceId || reviewData.serviceId.trim() === '') {
          addNotification('error', 'Service ID is required to submit a review.');
          return;
        }
        if (!reviewData.rating || reviewData.rating < 1 || reviewData.rating > 4) {
          addNotification('error', 'Please provide a valid rating between 1 and 4.');
          return;
        }
        if (!reviewData.title || reviewData.title.trim() === '') {
          addNotification('error', 'Please provide a review title.');
          return;
        }
        if (!reviewData.review || reviewData.review.trim() === '') {
          addNotification('error', 'Please write your review.');
          return;
        }
        if (reviewData.review.trim().length < 10 || reviewData.review.trim().length > 2000) {
          addNotification('error', 'Review must be between 10 and 2000 characters.');
          return;
        }

        // Log the review data for debugging
        console.log('Creating review with data:', {
          ...reviewData,
          imageCount: reviewData.images?.length || 0,
          imageNamesCount: reviewData.imageNames?.length || 0,
          imageUrlsCount: reviewData.imageUrls?.length || 0
        });

        const createData: CreateReviewData = {
          providerId: reviewData.providerId || 'default-provider',
          serviceId: reviewData.serviceId.trim(),
          serviceName: reviewData.serviceName || 'Service Review',
          bookingId: reviewData.bookingId || 'booking-' + Date.now(), // Generate a booking ID for demo
          title: reviewData.title.trim(),
          review: reviewData.review.trim(),
          comment: reviewData.review.trim(), // Backend expects comment field
          rating: Math.round(reviewData.rating), // Ensure integer rating
          images: reviewData.images || [], // Original File objects (for reference)
          imageUrls: reviewData.imageUrls || [], // S3 URLs (for display)
          imageNames: reviewData.imageNames || [], // S3 image names (for backend storage)
          // Add user information from auth context
          userName: (auth.user as any)?.name || (auth.user as any)?.email || 'User',
          userEmail: (auth.user as any)?.email || '',
          userProfileImage: (auth.user as any)?.picture || '',
          isVerified: (auth.user as any)?.email_verified || false,
          // Add current timestamp
          date: currentDate,
          createdAt: currentDate,
        };

        console.log('Final create data being sent to backend:', {
          ...createData,
          imageNamesArray: createData.imageNames,
          imageNamesCount: createData.imageNames.length
        });

        // Include rating fields - backend requires all ratings including timelinessRating
        if (reviewData.serviceRating && reviewData.serviceRating >= 1 && reviewData.serviceRating <= 4) {
          createData.serviceRating = Math.round(reviewData.serviceRating);
        }
        if (reviewData.qualityRating && reviewData.qualityRating >= 1 && reviewData.qualityRating <= 4) {
          createData.qualityRating = Math.round(reviewData.qualityRating);
        }
        if (reviewData.valueRating && reviewData.valueRating >= 1 && reviewData.valueRating <= 4) {
          createData.valueRating = Math.round(reviewData.valueRating);
        }
        if (reviewData.communicationRating && reviewData.communicationRating >= 1 && reviewData.communicationRating <= 4) {
          createData.communicationRating = Math.round(reviewData.communicationRating);
        }

        // Backend requires timelinessRating - use a default value if not provided
        if (reviewData.timelinessRating && reviewData.timelinessRating >= 1 && reviewData.timelinessRating <= 4) {
          createData.timelinessRating = Math.round(reviewData.timelinessRating);
        } else {
          // Set default timelinessRating to match the overall rating if not provided
          createData.timelinessRating = Math.round(reviewData.rating);
        }

        console.log('Submitting review to backend...');
        const createdReview = await createReview(createData);
        console.log('Review created successfully:', createdReview);

        // Refresh reviews to show the new one
        await fetchReviews(1); // Go to first page to see new review

        // Switch to show-reviews tab to display the newly submitted review
        setActiveTab('show-reviews');

        // Show enhanced success notification with image info
        const imageCount = createData.imageNames.length;
        const successMessage = imageCount > 0
          ? `Your review for "${reviewData.title}" has been submitted successfully with ${imageCount} image${imageCount > 1 ? 's' : ''} uploaded to S3!`
          : `Your review for "${reviewData.title}" has been submitted successfully.`;

        addNotification('success', successMessage);
      }
    } catch (error: any) { 
      console.error('Error submitting review:', error);

      // Enhanced error handling for different types of failures
      let errorMessage = 'Failed to submit review. Please try again.';

      if (error.response?.status === 422) {
        // Validation error from backend
        const validationErrors = error.response?.data?.formattedErrors || [];
        if (validationErrors.length > 0) {
          const errorMessages = validationErrors.map((err: any) => err.message).join(', ');
          errorMessage = `Validation error: ${errorMessages}`;
        } else {
          errorMessage = 'Please check your review data and try again.';
        }
      } else if (error.response?.status === 413) {
        // File too large
        errorMessage = 'One or more images are too large. Please use smaller images (max 5MB each).';
      } else if (error.message?.includes('S3') || error.message?.includes('upload')) {
        // S3 upload specific error
        errorMessage = 'Failed to upload images to S3. Please check your images and try again.';
      } else if (error.response?.data?.message) {
        // Backend provided specific error message
        errorMessage = error.response.data.message;
      }

      addNotification('error', errorMessage);
    } finally {
      setLoading(false);

      // Reset form state
      setShowReviewForm(false);
      setSelectedReview(null);
      setIsEditMode(false);
    }
  };

  // Handle delete review with confirmation
  const handleDeleteReviewWithConfirmation = async (reviewId: string, reviewTitle: string) => {
    // Debug: Log the review ID to see what we're getting
    console.log('Delete review called with ID:', reviewId, 'Title:', reviewTitle);

    if (!reviewId || reviewId === 'undefined') {
      console.error('Invalid review ID:', reviewId);
      addNotification('error', 'Cannot delete review: Invalid review ID');
      return;
    }

    const confirmed = window.confirm(
      `Are you sure you want to delete the review "${reviewTitle}"? This action cannot be undone.`
    );

    if (!confirmed) return;

    try {
      setLoading(true);
      setError(null);

      await deleteReview(reviewId);
      addNotification('success', 'Review deleted successfully!');

      // Refresh reviews list
      await fetchReviews(currentPage);
    } catch (error: any) {
      console.error('Error deleting review:', error);
      addNotification('error', error.response?.data?.message || 'Failed to delete review');
    } finally {
      setLoading(false);
    }
  };



  // Notification component
  const NotificationComponent = ({ notification }: { notification: Notification }) => {
    const { type, message, id } = notification;

    // Define icon and colors based on notification type
    const getNotificationStyles = () => {
      switch (type) {
        case 'success':
          return {
            bgColor: 'bg-green-100',
            textColor: 'text-green-800',
            borderColor: 'border-green-200',
            icon: <FiCheckCircle className="w-5 h-5 text-green-500" />
          };
        case 'error':
          return {
            bgColor: 'bg-red-100',
            textColor: 'text-red-800',
            borderColor: 'border-red-200',
            icon: <FiXCircle className="w-5 h-5 text-red-500" />
          };
        case 'warning':
          return {
            bgColor: 'bg-yellow-100',
            textColor: 'text-yellow-800',
            borderColor: 'border-yellow-200',
            icon: <FiAlertCircle className="w-5 h-5 text-yellow-500" />
          };
        case 'info':
        default:
          return {
            bgColor: 'bg-blue-100',
            textColor: 'text-blue-800',
            borderColor: 'border-blue-200',
            icon: <FiAlertCircle className="w-5 h-5 text-blue-500" />
          };
      }
    };

    const styles = getNotificationStyles();

    return (
      <div
        key={id}
        className={`flex items-center p-4 mb-3 rounded-lg border ${styles.bgColor} ${styles.textColor} ${styles.borderColor} animate-fadeIn`}
        role="alert"
      >
        <div className="mr-2">{styles.icon}</div>
        <div className="text-sm font-medium">{message}</div>
        <button
          type="button"
          className="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 hover:bg-gray-200 focus:outline-none"
          onClick={() => setNotifications(prev => prev.filter(n => n.id !== id))}
          aria-label="Close"
        >
          <FiXCircle className="w-5 h-5" />
        </button>
      </div>
    );
  };

  return (
    <div className="mx-auto p-6 bg-gray-50 min-h-screen relative">
      {/* Notifications container */}
      <div className="fixed top-4 right-4 z-50 w-80 max-w-full">
        {notifications.map(notification => (
          <NotificationComponent key={notification.id} notification={notification} />
        ))}
      </div>

      <BreadCrumb title="Reviews Dashboard" item1="Customer" />
{/* 
      User Profile Header
      {auth.isAuthenticated && auth.user && (
        <div className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-100">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img
                src={(auth.user as any)?.picture || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face'}
                alt={`${(auth.user as any)?.name || 'User'}'s profile`}
                className="w-16 h-16 rounded-full border-3 border-white shadow-lg object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=64&h=64&fit=crop&crop=face';
                }}
              />
              {(auth.user as any)?.email_verified && (
                <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-1">
                <h2 className="text-xl font-semibold text-gray-900">
                  {(auth.user as any)?.name || (auth.user as any)?.email || 'User'}
                </h2>
                {(auth.user as any)?.email_verified && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Verified User
                  </span>
                )}
              </div>
              <p className="text-gray-600 text-sm">
                {(auth.user as any)?.email}
              </p>
            </div>
          </div>
        </div>
      )} */}

      {/* Dashboard Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <div>
          <h2 className="text-3xl font-bold text-gray-800 mb-2">Reviews Dashboard</h2>
          <p className="text-gray-600">Manage and view all your service reviews</p>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="mb-6">
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={(key) => setActiveTab(key as string)}
          color="primary"
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
            tabContent: "group-data-[selected=true]:text-primary"
          }}
        >
           <Tab
            key="submit-review"
            title={
              <div className="flex items-center space-x-2">
                <FiEdit />
                <span>Submit Review</span>
              </div>
            }
          />
          <Tab
            key="show-reviews"
            title={
              <div className="flex items-center space-x-2">
                <FiEye />
                <span>Show Reviews</span>
              </div>
            }
          />
        </Tabs>
      </div>

      {/* Tab Content */}
      {activeTab === 'submit-review' && (
        <div className="space-y-6">
          {/* Submit Review Form */}
          <Card className="bg-white shadow-lg">
            <CardBody className="p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">Submit a New Review</h3>
                <p className="text-gray-600">Share your experience with our services and help others make informed decisions</p>
              </div>

              {/* Review Form Preview */}
              <div className="max-w-4xl mx-auto">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Left Column - Form Preview */}
                  <div className="space-y-6">
                    <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-100">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <FiEdit className="mr-2 text-blue-600" />
                        What You'll Share
                      </h4>
                      <div className="space-y-3 text-sm text-gray-600">
                        <div className="flex items-center">
                          <FiStar className="mr-2 text-yellow-500" />
                          <span>Rate your overall experience (1-5 stars)</span>
                        </div>
                        <div className="flex items-center">
                          <FiEdit className="mr-2 text-blue-500" />
                          <span>Write a descriptive title for your review</span>
                        </div>
                        <div className="flex items-center">
                          <FiCheckCircle className="mr-2 text-green-500" />
                          <span>Share detailed feedback about the service</span>
                        </div>
                        <div className="flex items-center">
                          <FiEye className="mr-2 text-purple-500" />
                          <span>Add photos (max 5 images) - automatically uploaded to S3</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg border border-green-100">
                      <h4 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <FiCheckCircle className="mr-2 text-green-600" />
                        Why Your Review Matters
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600">
                        <li>• Helps other customers make informed decisions</li>
                        <li>• Provides valuable feedback to service providers</li>
                        <li>• Builds trust within our community</li>
                        <li>• Improves overall service quality</li>
                      </ul>
                    </div>
                  </div>

                  {/* Right Column - Action Button */}
                  <div className="flex flex-col justify-center">
                    <div className="text-center mb-6">
                      <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                        <FiEdit className="w-8 h-8 text-blue-600" />
                      </div>
                      <h4 className="text-xl font-semibold text-gray-800 mb-2">Ready to Share?</h4>
                      <p className="text-gray-600 mb-6">Your feedback takes just a few minutes and makes a big difference</p>
                    </div>

                    <Button
                      color="primary"
                      size="lg"
                      className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
                      startContent={<FiEdit className="w-5 h-5" />}
                      onPress={() => {
                        setIsEditMode(false);
                        setSelectedReview(null);
                        setShowReviewForm(true);
                        addNotification('info', 'Ready to write a review! Please fill out the form to share your experience.');
                      }}
                      isDisabled={loading}
                    >
                      {loading ? 'Processing...' : 'Create Review & Upload Photos'}
                    </Button>

                    <div className="mt-4 text-center text-sm text-gray-500">
                      <p>✨ Your review will be visible to other customers</p>
                      <p className="mt-1">📝 Takes about 2-3 minutes to complete</p>
                      <p className="mt-1">📸 Photos are automatically uploaded to S3 cloud storage</p>
                      <p className="mt-1">🔒 Maximum 5 images per review (5MB each)</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      )}

      {activeTab === 'show-reviews' && (
        <div className="space-y-6">
          {/* Beautiful Header Section */}
          <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-3 bg-blue-100 rounded-full">
                    <FiEye className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold text-gray-800">Customer Reviews</h3>
                    <p className="text-gray-600">Discover authentic experiences from our valued customers</p>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="flex flex-wrap gap-4 mt-4">
                  <div className="flex items-center gap-2 bg-white bg-opacity-60 px-4 py-2 rounded-full">
                    <FiStar className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm font-medium text-gray-700">
                      {totalReviews} Total Reviews
                    </span>
                  </div>
                  <div className="flex items-center gap-2 bg-white bg-opacity-60 px-4 py-2 rounded-full">
                    <FiCheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Verified Customers
                    </span>
                  </div>
                  <div className="flex items-center gap-2 bg-white bg-opacity-60 px-4 py-2 rounded-full">
                    <FiEye className="w-4 h-4 text-blue-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Real Experiences
                    </span>
                  </div>
                </div>
              </div>

              {/* Action Button */}
              <div className="flex-shrink-0">
                <Button
                  color="primary"
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  startContent={<FiEdit className="w-5 h-5" />}
                  onPress={() => {
                    setActiveTab('submit-review');
                    addNotification('info', 'Ready to share your experience? Fill out the form to submit your review.');
                  }}
                >
                  Share Your Experience
                </Button>
              </div>
            </div>
          </div>

      {/* Review Statistics Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm font-medium">Total Reviews</p>
                <p className="text-3xl font-bold">{reviewStats.totalCount}</p>
              </div>
              <div className="bg-blue-400 bg-opacity-30 p-3 rounded-full">
                <FiStar className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-100 text-sm font-medium">Average Rating</p>
                <p className="text-3xl font-bold">{reviewStats.averageRating}</p>
              </div>
              <div className="bg-green-400 bg-opacity-30 p-3 rounded-full">
                <FiCheckCircle className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-100 text-sm font-medium">5-Star Reviews</p>
                <p className="text-3xl font-bold">
                  {reviewStats.ratingDistribution.find(r => r.rating === 5)?.count || 0}
                </p>
              </div>
              <div className="bg-purple-400 bg-opacity-30 p-3 rounded-full">
                <FiStar className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-100 text-sm font-medium">This Month</p>
                <p className="text-3xl font-bold">
                  {reviews.filter(r => new Date(r.date).getMonth() === new Date().getMonth()).length}
                </p>
              </div>
              <div className="bg-orange-400 bg-opacity-30 p-3 rounded-full">
                <FiAlertCircle className="w-6 h-6" />
              </div>
            </div>
          </CardBody>
        </Card> */}
      </div>

          {/* Enhanced Filters and Controls */}
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardBody className="p-6">
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
                {/* Filter Section */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-1">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800">Filter & Sort</h3>
                  </div>

                  <div className="flex flex-wrap items-center gap-3">
                    <Select
                      placeholder="All Ratings"
                      className="w-40"
                      selectedKeys={[filterRating]}
                      onSelectionChange={(keys) => setFilterRating(Array.from(keys)[0] as string)}
                      classNames={{
                        trigger: "bg-gray-50 border-gray-200 hover:bg-gray-100 transition-colors",
                      }}
                    >
                      <SelectItem key="all" value="all">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-gray-500" />
                          All Ratings
                        </div>
                      </SelectItem>
                      <SelectItem key="5" value="5">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-yellow-500" />
                          5 Stars
                        </div>
                      </SelectItem>
                      <SelectItem key="4" value="4">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-yellow-500" />
                          4 Stars
                        </div>
                      </SelectItem>
                      <SelectItem key="3" value="3">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-yellow-500" />
                          3 Stars
                        </div>
                      </SelectItem>
                      <SelectItem key="2" value="2">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-yellow-500" />
                          2 Stars
                        </div>
                      </SelectItem>
                      <SelectItem key="1" value="1">
                        <div className="flex items-center gap-2">
                          <FiStar className="w-4 h-4 text-yellow-500" />
                          1 Star
                        </div>
                      </SelectItem>
                    </Select>

                    {filterRating !== 'all' && (
                      <Chip
                        color="primary"
                        variant="flat"
                        onClose={() => setFilterRating('all')}
                        className="bg-blue-100 text-blue-800"
                      >
                        {filterRating} Star{filterRating !== '1' ? 's' : ''} Only
                      </Chip>
                    )}
                  </div>
                </div>

                {/* View Mode and Per Page Controls */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  {/* View Mode Toggle */}
                  <div className="flex items-center gap-2 bg-gray-100 p-1 rounded-lg">
                    <Button
                      size="sm"
                      variant={viewMode === 'grid' ? 'solid' : 'light'}
                      color={viewMode === 'grid' ? 'primary' : 'default'}
                      onPress={() => setViewMode('grid')}
                      className={`min-w-unit-20 ${viewMode === 'grid' ? 'shadow-md' : ''}`}
                      startContent={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      }
                    >
                      Grid
                    </Button>
                    <Button
                      size="sm"
                      variant={viewMode === 'list' ? 'solid' : 'light'}
                      color={viewMode === 'list' ? 'primary' : 'default'}
                      onPress={() => setViewMode('list')}
                      className={`min-w-unit-20 ${viewMode === 'list' ? 'shadow-md' : ''}`}
                      startContent={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                        </svg>
                      }
                    >
                      List
                    </Button>
                  </div>

                  {/* Per Page Selector */}
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span className="whitespace-nowrap">Show:</span>
                    <select
                      value={reviewsPerPage}
                      onChange={(e) => handleReviewsPerPageChange(parseInt(e.target.value))}
                      disabled={loading}
                      className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white hover:bg-gray-50 transition-colors"
                    >
                      <option value={3}>3 per page</option>
                      <option value={6}>6 per page</option>
                      <option value={9}>9 per page</option>
                      <option value={12}>12 per page</option>
                    </select>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

      {/* Loading State */}
      {loading && (
        <div className="bg-white p-8 rounded-xl shadow-md text-center">
          <Spinner size="lg" color="primary" />
          <p className="text-gray-600 mt-4">Loading reviews...</p>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="bg-white p-8 rounded-xl shadow-md text-center">
          <FiAlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">Error Loading Reviews</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button
            color="primary"
            onPress={() => fetchReviews(currentPage)}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* No Reviews State */}
      {!loading && !error && filteredReviews.length === 0 && (
        <Card className="text-center">
          <CardBody className="p-12">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FiStar className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-700 mb-3">
              {filterRating === 'all' ? 'No Reviews Available' : `No ${filterRating}-Star Reviews`}
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              {filterRating === 'all'
                ? "No reviews have been submitted yet. Be the first to share your experience!"
                : `No ${filterRating}-star reviews found. Try adjusting your filter to see other reviews.`
              }
            </p>
            <div className="flex gap-3 justify-center">
              {filterRating !== 'all' && (
                <Button
                  color="secondary"
                  variant="bordered"
                  onPress={() => setFilterRating('all')}
                >
                  Show All Reviews
                </Button>
              )}

              {filterRating === 'all' && (
                <Button
                  color="primary"
                  startContent={<FiEdit />}
                  onPress={() => {
                    setActiveTab('submit-review');
                    addNotification('info', 'Ready to write your first review! Please fill out the form to share your experience.');
                  }}
                >
                  Submit Your First Review
                </Button>
              )}
            </div>
          </CardBody>
        </Card>
      )}

      {/* Reviews List */}
      {!loading && !error && filteredReviews.length > 0 && (
        <div className="space-y-6 mt-4">
          {/* Display showing X of Y reviews */}
          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-600">
              Showing {((currentPage - 1) * reviewsPerPage) + 1}-{Math.min(currentPage * reviewsPerPage, totalReviews)} of {totalReviews} reviews
              {filterRating !== 'all' && (
                <Chip size="sm" color="primary" className="ml-2">
                  {filterRating} Star{filterRating !== '1' ? 's' : ''} Only
                </Chip>
              )}
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <span>{viewMode === 'grid' ? 'Grid View' : 'List View'}</span>
              <div className="flex items-center gap-2">
                <span>Show:</span>
                <select
                  value={reviewsPerPage}
                  onChange={(e) => handleReviewsPerPageChange(parseInt(e.target.value))}
                  disabled={loading}
                  className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={3}>3 per page</option>
                  <option value={6}>6 per page</option>
                  <option value={9}>9 per page</option>
                  <option value={12}>12 per page</option>
                </select>
              </div>
            </div>
          </div>

          <div className={viewMode === 'grid' ? 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-4'}>
            {currentReviews.map((review) => (
              <Card
                key={review.id}
                className="review-card hover:shadow-xl transition-all duration-300 border-0 bg-white/90 backdrop-blur-sm hover:bg-white hover:scale-[1.02] group"
              >
                <CardBody className="p-6 relative overflow-hidden">
                  {/* Decorative gradient overlay */}
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full -translate-y-10 translate-x-10 opacity-50 group-hover:opacity-70 transition-opacity"></div>
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="relative">
                      <img
                        src={
                          review.userProfileImage ||
                          review.profileImage ||
                          (auth.user as any)?.picture ||
                          'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face'
                        }
                        alt={`${review.userName || review.name || (auth.user as any)?.name || 'User'}'s profile`}
                        className="w-12 h-12 rounded-full border-2 border-gray-200 object-cover shadow-sm"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face';
                        }}
                      />
                      {/* Verified user indicator */}
                      {(review.isVerified || (auth.user as any)?.email_verified) && (
                        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
                          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">
                        {review.title}
                      </h3>
                      <div className="flex flex-col space-y-1 text-gray-600 text-sm mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-800">
                            {review.userName || review.name || (auth.user as any)?.name || (auth.user as any)?.email || 'User'}
                          </span>
                          {(review.isVerified || (auth.user as any)?.email_verified) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                              Verified
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span>Submitted on</span>
                          <span className="font-medium text-gray-700">
                            {new Date(review.createdAt || review.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </span>
                          <span>•</span>
                          <span className="text-gray-600">
                            {new Date(review.createdAt || review.date).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          {review.updatedAt && review.updatedAt !== (review.createdAt || review.date) && (
                            <>
                              <span>•</span>
                              <span className="text-orange-600 font-medium">Edited</span>
                            </>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1 bg-gradient-to-r from-yellow-50 to-orange-50 px-3 py-1.5 rounded-full border border-yellow-200">
                          {Array(4)
                            .fill(0)
                            .map((_, i) => (
                              <FiStar
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating
                                    ? 'text-yellow-500 fill-current drop-shadow-sm'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          <span className="ml-2 text-sm font-bold text-gray-800">
                            {review.rating}.0
                          </span>
                        </div>
                        <Chip size="sm" color="success" variant="flat" className="text-xs font-medium">
                          ✓ Verified
                        </Chip>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Review Content */}
                  <div className="bg-gray-50 rounded-xl p-4 mb-4 border border-gray-100">
                    <p className="text-gray-700 leading-relaxed line-clamp-3 text-sm">
                      "{review.review}"
                    </p>
                    {review.review && review.review.length > 150 && (
                      <button
                        onClick={() => handleViewReview(review)}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium mt-3 flex items-center gap-1 transition-colors group"
                      >
                        <span>Read full review</span>
                        <FiEye className="w-4 h-4 group-hover:scale-110 transition-transform" />
                      </button>
                    )}
                  </div>

                  {/* Display review images using the reusable component */}
                  <ReviewImages
                    imageNames={review.imageNames}
                    imageUrls={review.imageUrls}
                    maxDisplay={3}
                    size="md"
                    className="mb-4"
                    showCount={true}
                    folderName="review-images"
                  />

                  {/* Enhanced Footer with Actions */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-3">
                      <Chip
                        size="sm"
                        color={review.rating >= 4 ? 'success' : review.rating >= 3 ? 'warning' : 'danger'}
                        variant="flat"
                        className="font-medium"
                      >
                        {review.rating >= 4 ? '🌟 Excellent' : review.rating >= 3 ? '👍 Good' : '⚠️ Needs Improvement'}
                      </Chip>
                      {review.imageNames?.length > 0 && (
                        <Chip size="sm" color="primary" variant="flat" className="text-xs">
                          📸 {review.imageNames.length} Photo{review.imageNames.length > 1 ? 's' : ''}
                        </Chip>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="flat"
                        color="primary"
                        onPress={() => {
                          console.log('View button clicked for review:', review);
                          if (!loading) {
                            handleViewReview(review);
                          }
                        }}
                        isDisabled={loading}
                        className="bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200 font-medium transition-all duration-200 hover:scale-105"
                        startContent={<FiEye size={14} />}
                      >
                        View Details
                      </Button>

                      <Button
                        isIconOnly
                        size="sm"
                        variant="flat"
                        color="danger"
                        onPress={() => {
                          // Debug: Log the review object to see its structure
                          console.log('Delete button clicked for review:', review);
                          console.log('Review ID:', review.id);
                          console.log('Review _id:', review._id);

                          // Use _id if id is not available (MongoDB default)
                          const reviewId = review.id || review._id;
                          if (!loading && reviewId) {
                            handleDeleteReviewWithConfirmation(reviewId, review.title);
                          } else {
                            console.error('No valid review ID found:', { id: review.id, _id: review._id });
                            addNotification('error', 'Cannot delete review: Invalid review ID');
                          }
                        }}
                        isDisabled={loading}
                        title="Delete Review"
                        className="bg-red-50 hover:bg-red-100 text-red-600 border-red-200 transition-all duration-200 hover:scale-105"
                      >
                        <FiTrash size={14} />
                      </Button>
                    </div>
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Enhanced Pagination Controls */}
      {totalPages > 1 && (
        <div className="mt-8 space-y-4">
          {/* Pagination Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            {/* Previous/Next Navigation */}
            <div className="flex items-center gap-2">
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToFirstPage}
                isDisabled={loading || currentPage === 1}
                title="First Page"
              >
                <FiChevronsLeft size={16} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToPreviousPage}
                isDisabled={loading || currentPage === 1}
                title="Previous Page"
              >
                <FiChevronLeft size={16} />
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1 mx-2">
                {getPageNumbers().map((page, index) => (
                  <div key={index}>
                    {page === '...' ? (
                      <span className="px-2 py-1 text-gray-500">...</span>
                    ) : (
                      <Button
                        size="sm"
                        variant={currentPage === page ? "solid" : "bordered"}
                        color={currentPage === page ? "primary" : "default"}
                        onPress={() => handlePageChange(page as number)}
                        isDisabled={loading}
                        className={`min-w-[40px] ${
                          currentPage === page
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'hover:bg-blue-50 hover:border-blue-300'
                        }`}
                      >
                        {page}
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToNextPage}
                isDisabled={loading || currentPage === totalPages}
                title="Next Page"
              >
                <FiChevronRight size={16} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="bordered"
                color="primary"
                onPress={goToLastPage}
                isDisabled={loading || currentPage === totalPages}
                title="Last Page"
              >
                <FiChevronsRight size={16} />
              </Button>
            </div>

            {/* Page Info */}
            <div className="text-sm text-gray-600 flex items-center gap-4">
              <span>
                Showing {((currentPage - 1) * reviewsPerPage) + 1}-{Math.min(currentPage * reviewsPerPage, totalReviews)} of {totalReviews} reviews
              </span>
              <span className="hidden sm:inline">•</span>
              <span className="hidden sm:inline">
                Page {currentPage} of {totalPages}
              </span>
            </div>
          </div>

          {/* Alternative: HeroUI Pagination Component (for comparison) */}
          <div className="flex justify-center">
            <Pagination
              initialPage={1}
              page={currentPage}
              onChange={handlePageChange}
              total={totalPages}
              isDisabled={loading || totalPages <= 1}
              showControls
              color="primary"
              className="gap-2"
              size="sm"
            />
          </div>

          {/* Quick Page Jump */}
          {totalPages > 10 && (
            <div className="flex justify-center items-center gap-2 text-sm">
              <span className="text-gray-600">Go to page:</span>
              <select
                value={currentPage}
                onChange={(e) => handlePageChange(parseInt(e.target.value))}
                disabled={loading}
                className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                  <option key={page} value={page}>
                    {page}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Keyboard Navigation Hint */}
          {totalPages > 1 && (
            <div className="text-center text-xs text-gray-500 mt-2">
              💡 Use arrow keys (← →) to navigate pages, or Home/End for first/last page
            </div>
          )}
        </div>
      )}
        </div>
      )}

      {/* Enhanced Creative Review Detail Modal */}
      {showViewModal && selectedViewReview && (
        <Modal
          isOpen={showViewModal}
          onClose={handleCloseViewModal}
          size="4xl"
          scrollBehavior="inside"
          classNames={{
            base: "max-h-[95vh]",
            body: "p-0",
            header: "hidden",
            footer: "border-t-0 bg-transparent p-6"
          }}
        >
          <ModalContent className="bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 backdrop-blur-sm border-0 shadow-2xl">
            {/* Custom Header with Gradient Background */}
            <div className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 p-8 text-white overflow-hidden">
              {/* Decorative Background Elements */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

              {/* Close Button */}
              <button
                onClick={handleCloseViewModal}
                className="absolute top-4 right-4 p-2 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200 group"
              >
                <svg className="w-6 h-6 group-hover:rotate-90 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Header Content */}
              <div className="relative z-10">
                <div className="flex items-start gap-6">
                  {/* Profile Image with Glow Effect */}
                  <div className="relative">
                    <div className="absolute inset-0 bg-white/30 rounded-full blur-lg"></div>
                    <img
                      src={
                        selectedViewReview.userProfileImage ||
                        selectedViewReview.profileImage ||
                        'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face'
                      }
                      alt={`${selectedViewReview.userName || selectedViewReview.name || 'User'}'s profile`}
                      className="relative w-20 h-20 rounded-full border-4 border-white/50 object-cover shadow-xl"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=80&h=80&fit=crop&crop=face';
                      }}
                    />
                    {selectedViewReview.isVerified && (
                      <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full flex items-center justify-center shadow-lg">
                        <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* User Info and Title */}
                  <div className="flex-1">
                    <h2 className="text-3xl font-bold mb-2 text-white drop-shadow-lg">
                      {selectedViewReview.title}
                    </h2>
                    <div className="flex items-center gap-3 mb-4">
                      <span className="text-xl font-semibold text-white/90">
                        {selectedViewReview.userName || selectedViewReview.name || 'User'}
                      </span>
                      {selectedViewReview.isVerified && (
                        <Chip size="sm" className="bg-white/20 text-white border-white/30">
                          ✓ Verified Customer
                        </Chip>
                      )}
                    </div>

                    {/* Rating Display with Animation */}
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1 bg-white/20 px-4 py-2 rounded-full backdrop-blur-sm">
                        {Array(4).fill(0).map((_, i) => (
                          <FiStar
                            key={i}
                            className={`w-5 h-5 ${
                              i < selectedViewReview.rating
                                ? 'text-yellow-300 fill-current drop-shadow-sm'
                                : 'text-white/40'
                            } transition-all duration-200 hover:scale-110`}
                          />
                        ))}
                        <span className="ml-2 text-lg font-bold text-white">
                          {selectedViewReview.rating}.0 / 4.0
                        </span>
                      </div>
                      <div className="text-white/80 text-sm">
                        {new Date(selectedViewReview.createdAt || selectedViewReview.date).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Enhanced Modal Body */}
            <div className="p-8 space-y-8">
              {/* Review Content Section */}
              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Customer Experience
                  </h3>
                  {selectedViewReview.updatedAt && selectedViewReview.updatedAt !== (selectedViewReview.createdAt || selectedViewReview.date) && (
                    <Chip size="sm" color="warning" variant="flat" className="text-xs">
                      ✏️ Edited
                    </Chip>
                  )}
                </div>

                {/* Review Content */}
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold text-gray-900">Review Comment:</h4>
                      {!editingComment && (
                        <Button
                          size="sm"
                          variant="light"
                          color="primary"
                          startContent={<FiEdit size={14} />}
                          onPress={handleStartEditComment}
                          isDisabled={loading}
                          className="text-sm"
                        >
                          Edit Comment
                        </Button>
                      )}
                    </div>

                    {editingComment ? (
                      <div className="space-y-3">
                        <Textarea
                          value={editedComment}
                          onChange={(e) => setEditedComment(e.target.value)}
                          placeholder="Enter your review comment..."
                          minRows={4}
                          maxRows={8}
                          className="w-full"
                          classNames={{
                            input: "text-gray-700",
                            inputWrapper: "border-gray-300 focus:border-blue-500"
                          }}
                          description={`${editedComment.length}/2000 characters (minimum 10 characters)`}
                          isInvalid={editedComment.length < 10 || editedComment.length > 2000}
                          errorMessage={
                            editedComment.length < 10
                              ? "Comment must be at least 10 characters long"
                              : editedComment.length > 2000
                              ? "Comment must be less than 2000 characters"
                              : ""
                          }
                        />
                        <div className="flex gap-2 justify-end">
                          <Button
                            size="sm"
                            variant="light"
                            onPress={handleCancelEditComment}
                            isDisabled={loading}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            color="primary"
                            onPress={handleSaveComment}
                            isLoading={loading}
                            isDisabled={editedComment.length < 10 || editedComment.length > 2000}
                          >
                            Save Comment
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="bg-gradient-to-br from-gray-50 to-blue-50/50 rounded-xl p-6 border border-gray-200/50 relative">
                        <div className="flex items-start gap-4">
                          <div className="w-1 h-20 bg-gradient-to-b from-blue-400 to-purple-400 rounded-full flex-shrink-0 mt-1"></div>
                          <blockquote className="text-gray-700 leading-relaxed text-lg italic flex-1">
                            "{selectedViewReview.review || selectedViewReview.comment || 'No comment provided'}"
                          </blockquote>
                        </div>
                        <div className="absolute top-4 right-4 text-6xl text-blue-200/30 font-serif">"</div>
                      </div>
                    )}
                  </div>

              {/* Enhanced Detailed Ratings Section */}
              {(selectedViewReview.serviceRating || selectedViewReview.qualityRating ||
                selectedViewReview.valueRating || selectedViewReview.communicationRating ||
                selectedViewReview.timelinessRating) && (
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                  <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Detailed Service Ratings
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedViewReview.serviceRating && (
                      <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span className="font-semibold text-gray-800">Service Quality</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < selectedViewReview.serviceRating!
                                      ? 'text-yellow-500 fill-current drop-shadow-sm'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="font-bold text-gray-800 bg-white/60 px-2 py-1 rounded-full text-sm">
                              {selectedViewReview.serviceRating}/4
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedViewReview.qualityRating && (
                      <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-4 rounded-xl border border-green-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span className="font-semibold text-gray-800">Work Quality</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < selectedViewReview.qualityRating!
                                      ? 'text-yellow-500 fill-current drop-shadow-sm'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="font-bold text-gray-800 bg-white/60 px-2 py-1 rounded-full text-sm">
                              {selectedViewReview.qualityRating}/4
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedViewReview.valueRating && (
                      <div className="bg-gradient-to-br from-purple-50 to-pink-50 p-4 rounded-xl border border-purple-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span className="font-semibold text-gray-800">Value for Money</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < selectedViewReview.valueRating!
                                      ? 'text-yellow-500 fill-current drop-shadow-sm'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="font-bold text-gray-800 bg-white/60 px-2 py-1 rounded-full text-sm">
                              {selectedViewReview.valueRating}/4
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedViewReview.communicationRating && (
                      <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-4 rounded-xl border border-orange-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                            <span className="font-semibold text-gray-800">Communication</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < selectedViewReview.communicationRating!
                                      ? 'text-yellow-500 fill-current drop-shadow-sm'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="font-bold text-gray-800 bg-white/60 px-2 py-1 rounded-full text-sm">
                              {selectedViewReview.communicationRating}/4
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedViewReview.timelinessRating && (
                      <div className="bg-gradient-to-br from-teal-50 to-cyan-50 p-4 rounded-xl border border-teal-200/50 hover:shadow-md transition-all duration-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-teal-500 rounded-full"></div>
                            <span className="font-semibold text-gray-800">Timeliness</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              {Array(4).fill(0).map((_, i) => (
                                <FiStar
                                  key={i}
                                  className={`w-4 h-4 ${
                                    i < selectedViewReview.timelinessRating!
                                      ? 'text-yellow-500 fill-current drop-shadow-sm'
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                            <span className="font-bold text-gray-800 bg-white/60 px-2 py-1 rounded-full text-sm">
                              {selectedViewReview.timelinessRating}/4
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Enhanced Images Section */}
              {(selectedViewReview.imageNames?.length > 0 || selectedViewReview.imageUrls?.length > 0) && (
                <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                  <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <div className="w-2 h-2 bg-pink-500 rounded-full"></div>
                    Review Gallery
                    <Chip size="sm" color="primary" variant="flat" className="ml-2">
                      📸 {selectedViewReview.imageNames?.length || selectedViewReview.imageUrls?.length || 0} Photos
                    </Chip>
                  </h3>
                  <div className="bg-gradient-to-br from-gray-50 to-blue-50/30 rounded-xl p-4 border border-gray-200/50">
                    <ReviewImages
                      imageNames={selectedViewReview.imageNames}
                      imageUrls={selectedViewReview.imageUrls}
                      maxDisplay={4}
                      size="md"
                      showCount={true}
                      folderName="review-images"
                      layout="grid-4"
                      enablePreview={true}
                      className="w-full"
                    />
                  </div>
                </div>
              )}

              {/* Enhanced Metadata Section */}
              <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
                <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                  <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
                  Review Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-200/50">
                    <div className="flex items-center gap-2 mb-2">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-semibold text-gray-800">Submitted</span>
                    </div>
                    <p className="text-gray-700">
                      {new Date(selectedViewReview.createdAt || selectedViewReview.date).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                    <p className="text-sm text-gray-600">
                      {new Date(selectedViewReview.createdAt || selectedViewReview.date).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>

                  {selectedViewReview.updatedAt && selectedViewReview.updatedAt !== (selectedViewReview.createdAt || selectedViewReview.date) && (
                    <div className="bg-gradient-to-br from-orange-50 to-yellow-50 p-4 rounded-xl border border-orange-200/50">
                      <div className="flex items-center gap-2 mb-2">
                        <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        <span className="font-semibold text-gray-800">Last Updated</span>
                      </div>
                      <p className="text-gray-700">
                        {new Date(selectedViewReview.updatedAt).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                      <p className="text-sm text-gray-600">
                        {new Date(selectedViewReview.updatedAt).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Enhanced Modal Footer */}
            <ModalFooter className="bg-gradient-to-r from-gray-50 to-blue-50/30 border-t-0">
              <div className="flex justify-between items-center w-full">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Verified Customer Review</span>
                </div>
                <Button
                  color="primary"
                  variant="flat"
                  onPress={handleCloseViewModal}
                  className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Close Review
                </Button>
              </div>
            </ModalFooter>
          </ModalContent>
        </Modal>
      )}

      {/* Review Form Modal */}
      {showReviewForm && (
        <ReviewForm
          isOpen={showReviewForm}
          onClose={() => {
            setShowReviewForm(false);
            setSelectedReview(null);
            setIsEditMode(false);
          }}
          onSubmit={handleSubmitReview}
          providerId="default-provider"
          serviceId="default-service"
          bookingId="default-booking"
          serviceName="Service Review"
          initialData={
            isEditMode && selectedReview
              ? {
                  id: selectedReview.id || selectedReview._id,
                  providerId: selectedReview.providerId,
                  serviceId: selectedReview.serviceId,
                  serviceName: selectedReview.title,
                  rating: selectedReview.rating,
                  title: selectedReview.title,
                  review: selectedReview.review,
                  images: [],
                  imageUrls: selectedReview.imageUrls || [],
                  imageNames: selectedReview.imageNames || [], // Include image names
                  date: selectedReview.date,
                  serviceRating: selectedReview.serviceRating,
                  qualityRating: selectedReview.qualityRating,
                  valueRating: selectedReview.valueRating,
                  communicationRating: selectedReview.communicationRating,
                  timelinessRating: selectedReview.timelinessRating,
                }
              : undefined
          }
          isEdit={isEditMode}
        />
      )}
    </div>
  );
};

export default Reviews;
